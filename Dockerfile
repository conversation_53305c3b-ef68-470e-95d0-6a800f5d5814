## Build
FROM golang:1.24.5 AS build

WORKDIR /app

COPY . ./

ARG CODE_GITHUB_TOKEN
ENV GOPRIVATE=github.com/Sider-ai/*
RUN git config --global url."https://${CODE_GITHUB_TOKEN}@github.com/".insteadOf "https://github.com/" && \
    git config --global credential.helper store && \
    echo "https://${CODE_GITHUB_TOKEN}:<EMAIL>" > ~/.git-credentials && \
    chmod 600 ~/.git-credentials && \
    go mod download && \
    rm -f ~/.git-credentials && \
    git config --global --unset credential.helper && \
    git config --global --unset url."https://${CODE_GITHUB_TOKEN}@github.com/".insteadOf

RUN go mod download
RUN make build

## Deploy
FROM gcr.io/distroless/base-debian11

WORKDIR /

COPY --from=build /etc/ssl/certs/ca-certificates.crt /etc/ssl/certs/
COPY --from=build /app/configs /configs
COPY --from=build /app/output/server /server

EXPOSE 8282

ENTRYPOINT ["/server"]
