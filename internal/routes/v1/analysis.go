package v1

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"

	"web-coder-app/internal/data"
	"web-coder-app/internal/dto/request"
	"web-coder-app/internal/dto/response"
	"web-coder-app/internal/routes/common"
	"web-coder-app/internal/routes/middleware"
	"web-coder-app/internal/service"
	"web-coder-app/pkg/ecode"
	"web-coder-app/pkg/jwt"
)

type AnalysisHandler struct {
	analysisService *service.AnalysisService
	logger          *zap.SugaredLogger
	jwt             *jwt.JWT
	userRepo        *data.UserRepo
}

func NewAnalysisHandler(opt Options) *AnalysisHandler {
	return &AnalysisHandler{
		analysisService: opt.AnalysisService,
		logger:          opt.Log,
		jwt:             opt.Jwt,
		userRepo:        opt.UserRepo,
	}
}

// RegisterRoute implements the Registrable interface
func (h *AnalysisHandler) RegisterRoute(r *gin.RouterGroup) {
	v1Group := r.Group("/v1")
	analysisGroup := v1Group.Group("/analysis")
	analysisGroup.Use(middleware.TokenAuth(true, h.jwt, h.userRepo))
	{
		analysisGroup.POST("", h.AnalyzeFiles)
	}
}

// AnalyzeFiles handles file analysis requests
func (h *AnalysisHandler) AnalyzeFiles(c *gin.Context) {
	user := common.GetCurrentUserInfo(c)
	if user == nil {
		h.logger.Error("User not found in context")
		c.JSON(http.StatusUnauthorized, response.Response{
			Code: ecode.InvalidToken.Code,
			Msg:  "User not authenticated",
		})
		return
	}

	var req request.AnalysisRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Errorw("Failed to bind analysis request", "error", err)
		c.JSON(http.StatusBadRequest, response.Response{
			Code: ecode.InvalidParams.Code,
			Msg:  "Invalid request format: " + err.Error(),
		})
		return
	}

	// Validate request
	if err := req.Validate(); err != nil {
		h.logger.Errorw("Analysis request validation failed", "error", err)
		c.JSON(http.StatusBadRequest, response.Response{
			Code: ecode.InvalidParams.Code,
			Msg:  err.Error(),
		})
		return
	}

	// Perform analysis
	result, err := h.analysisService.AnalyzeFiles(c.Request.Context(), user, &req)
	if err != nil {
		h.logger.Errorw("File analysis failed", "error", err, "user_id", user.ID)
		c.JSON(http.StatusInternalServerError, response.Response{
			Code: ecode.InternalErr.Code,
			Msg:  "Analysis failed: " + err.Error(),
		})
		return
	}

	// Return success response
	common.SuccessResp(c, result)
}
