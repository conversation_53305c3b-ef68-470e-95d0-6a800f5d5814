package clients

import (
	"context"
	"errors"

	"github.com/Sider-ai/go-pkg/siderllm"
	sidererr "github.com/Sider-ai/sider-errors"
	"github.com/google/wire"
	"github.com/oschwald/maxminddb-golang"
	"github.com/redis/go-redis/v9"
	"go.uber.org/zap"

	"web-coder-app/configs"
	"web-coder-app/internal/clients/hashid"
	"web-coder-app/internal/clients/logdbsync"
	"web-coder-app/internal/clients/serverconf"
	"web-coder-app/pkg/jwt"
	"web-coder-app/pkg/litellm"
	"web-coder-app/pkg/logger"
)

var ProviderSet = wire.NewSet(
	NewGeoLite2,
	NewRDSClient,
	NewLogger,
	NewJwt,
	hashid.NewHashID,
	serverconf.NewServerConf,
	logdbsync.NewClient,
	NewLiteLLMClient,
	NewSiderLLMClient,
)

func NewGeoLite2(conf *configs.Config) (*maxminddb.Reader, error) {
	r, err := maxminddb.Open(conf.App.GeoLite2DB)
	if err != nil {
		return nil, sidererr.WithCaller(err)
	}
	return r, nil
}

// NewLiteLLMClient creates a new LiteLLM client with Anthropic provider
func NewLiteLLMClient(conf *configs.Config) (*litellm.Client, error) {
	client := litellm.New(
		litellm.WithGLM(conf.Claude.APIKey, conf.Claude.BaseURL),
	)

	return client, nil
}

func NewSiderLLMClient(conf *configs.Config) *siderllm.Client {
	return siderllm.NewClient(&conf.SiderLLM)
}

type RDS struct {
	Rds *redis.Client
	// 只做cache用
	CacheRDS *redis.Client
}

func NewJwt(conf *configs.Config) (*jwt.JWT, error) {
	return jwt.NewJWT(&jwt.Config{Issuer: conf.Jwt.Issuer, Secret: conf.Jwt.Secret})
}

func NewRDSClient(ctx context.Context, conf *configs.Config) (*RDS, error) {
	rds := redis.NewClient(&redis.Options{
		Addr:     conf.Redis.Addr,
		Password: conf.Redis.Password,
		DB:       conf.Redis.DB,
	})
	_, err := rds.Ping(ctx).Result()
	if err != nil {
		return nil, errors.Join(err, errors.New("new redis client"))
	}
	cacheRDS := redis.NewClient(&redis.Options{
		Addr:     conf.CacheRedis.Addr,
		Password: conf.CacheRedis.Password,
		DB:       conf.CacheRedis.DB,
	})
	// _, err = cacheRDS.Ping(ctx).Result()
	// if err != nil {
	//	fmt.Println("new cache redis client")
	// }
	return &RDS{
		Rds:      rds,
		CacheRDS: cacheRDS,
	}, nil
}

func NewLogger(conf *configs.Config, dbWrite *logdbsync.Client) *zap.SugaredLogger {
	logConf := conf.Log
	logConf.DBWriteSyncer = dbWrite
	return logger.NewLogger(&logConf)
}

//func NewRedisClient(ctx context.Context, conf *configs.Config) (*redis.Client, error) {
//	rdb := redis.NewClient(&redis.Options{
//		Addr:     conf.Redis.Addr,
//		Password: conf.Redis.Password,
//		DB:       conf.Redis.DB,
//	})
//	_, err := rdb.Ping(ctx).Result()
//	if err != nil {
//		return nil, errors.Join(err, errors.New("new redis client"))
//	}
//	return rdb, nil
//}
