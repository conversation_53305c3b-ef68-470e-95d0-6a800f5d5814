package request

import (
	"fmt"
	"web-coder-app/pkg/ecode"
)

// AnalysisItem represents a single code file item for analysis
type AnalysisItem struct {
	CodeDescription string `json:"codeDescription" binding:"required"`
	CodeFileName    string `json:"codeFileName" binding:"required"`
}

// AnalysisRequest represents the analysis request from client
type AnalysisRequest struct {
	Items       []AnalysisItem `json:"items" binding:"required,min=1"`
	UserContext string         `json:"userContext" binding:"required"`
}

// Validate validates the analysis request
func (r *AnalysisRequest) Validate() error {
	if len(r.Items) == 0 {
		return ecode.NewInvalidParamsErr("items cannot be empty")
	}

	if r.UserContext == "" {
		return ecode.NewInvalidParamsErr("userContext cannot be empty")
	}

	for i, item := range r.Items {
		if item.CodeDescription == "" {
			return ecode.NewInvalidParamsErr(fmt.Sprintf("codeDescription cannot be empty at index %d", i))
		}
		if item.CodeFileName == "" {
			return ecode.NewInvalidParamsErr(fmt.Sprintf("codeFileName cannot be empty at index %d", i))
		}
	}

	return nil
}
