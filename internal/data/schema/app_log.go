package schema

import (
	"web-coder-app/pkg/entutil"

	"entgo.io/ent"
	"entgo.io/ent/schema/field"
	"entgo.io/ent/schema/index"
)

type AppLog struct {
	ent.Schema
}

func (AppLog) Mixin() []ent.Mixin {
	return []ent.Mixin{
		entutil.CreateTime{},
	}
}

func (AppLog) Fields() []ent.Field {
	return []ent.Field{
		field.Int("user_id").Default(0),
		field.String("trace_id").Default(""),
		field.String("ip").Default(""),
		field.String("method").Default(""),
		field.String("path").Default(""),
		field.Text("query").Default(""),
		field.Text("body").Default(""),
		field.Enum("level").Values("ERROR", "WARN", "INFO", "DEBUG", "PANIC").<PERSON><PERSON><PERSON>("ERROR"),
		field.Enum("from_source").Values("api", "log").Default("api"),
		field.Text("err_msg"),
		field.Text("resp_err_msg").Default(""),
		field.Int("code").Optional().Default(0),
		field.String("service_name").Default(""),
		field.JSON("extra", map[string]any{}),
	}
}

func (AppLog) Edges() []ent.Edge {
	return []ent.Edge{}
}

func (AppLog) Indexes() []ent.Index {
	return []ent.Index{
		index.Fields("user_id"),
		index.Fields("level"),
		index.Fields("path"),
		index.Fields("code"),
	}
}
