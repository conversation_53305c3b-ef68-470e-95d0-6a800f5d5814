package service

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"github.com/Sider-ai/go-pkg/siderllm"
	"go.uber.org/zap"

	"web-coder-app/configs"
	"web-coder-app/internal/data/ent"
	"web-coder-app/internal/dto/request"
	"web-coder-app/internal/dto/response"
	"web-coder-app/pkg/litellm"
)

type AnalysisService struct {
	logger          *zap.SugaredLogger
	conf            *configs.Config
	llmClient       *litellm.Client
	siderChatClient *siderllm.Client
}

func NewAnalysisService(opt *Options, llmClient *litellm.Client) *AnalysisService {
	return &AnalysisService{
		logger:          opt.Log,
		conf:            opt.Conf,
		llmClient:       llmClient,
		siderChatClient: opt.SiderChatClient,
	}
}

// AnalyzeFiles analyzes files based on user context and returns relevant file names
func (s *AnalysisService) AnalyzeFiles(ctx context.Context, user *ent.User, req *request.AnalysisRequest) (*response.AnalysisResponse, error) {
	s.logger.Infow("Starting file analysis", "user_id", user.ID, "items_count", len(req.Items), "user_context", req.UserContext)

	maxTokens := s.conf.ChatModel.MaxTokens
	if maxTokens <= 0 {
		maxTokens = 2048
	}

	systemPrompt, err := s.buildSysPrompt(ctx, req.Items)
	if err != nil {
		return nil, fmt.Errorf("failed to build system prompt: %w", err)
	}

	llmReq := &litellm.Request{
		Model: s.conf.ChatModel.Chat,
		Messages: []litellm.Message{
			{
				Role:    "system",
				Content: systemPrompt,
			},
			{
				Role:    "user",
				Content: req.UserContext,
			},
		},
		Stream:         false,
		Temperature:    litellm.Float64Ptr(0.3), // Lower temperature for more consistent analysis
		MaxTokens:      litellm.IntPtr(maxTokens),
		ResponseFormat: litellm.NewResponseFormatJSONObject(),
	}

	resp, err := s.llmClient.Complete(ctx, llmReq)
	if err != nil {
		s.logger.Errorw("LLM analysis failed", "error", err, "user_id", user.ID)
		return nil, fmt.Errorf("failed to analyze files: %w", err)
	}

	relevantFiles, err := s.parseAnalysisResponse(resp.Content, req.Items)
	if err != nil {
		s.logger.Errorw("Failed to parse analysis response", "error", err, "response", resp.Content)
		return nil, fmt.Errorf("failed to parse analysis response: %w", err)
	}

	s.logger.Infow("File analysis completed",
		"user_id", user.ID,
		"total_files", len(req.Items),
		"relevant_files", len(relevantFiles),
		"result", relevantFiles)

	return response.NewAnalysisResponse(relevantFiles), nil
}

// buildSysPrompt builds system prompt from service
func (s *AnalysisService) buildSysPrompt(ctx context.Context, codeInformation []request.AnalysisItem) (string, error) {
	sysPromptTemplates, err := s.siderChatClient.ConvertPromptTemplate(ctx, &siderllm.PromptTemplateReq{
		PromptTemplates: []siderllm.PromptTemplateParam{
			{
				Key: "agent_web_coder_analysis",
				Attributes: map[string]string{
					"today": time.Now().Format(time.DateOnly),
				},
			},
		},
	})
	if err != nil {
		s.logger.Error("convert prompt template error", "err", err)
		return "", err
	}
	codeInfo, err := json.Marshal(codeInformation)
	if err != nil {
		return "", err
	}
	prompt := strings.ReplaceAll(sysPromptTemplates.SystemPrompt, "${CODE_INFO}", string(codeInfo))
	return prompt, nil
}

// parseAnalysisResponse parses the LLM response and extracts relevant file names
func (s *AnalysisService) parseAnalysisResponse(content string, items []request.AnalysisItem) ([]string, error) {
	content = strings.TrimSpace(content)
	fmt.Println("+++++++++++++++++++++++")
	fmt.Println(content)
	fmt.Println("+++++++++++++++++++++++")

	startIdx := strings.Index(content, "[")
	endIdx := strings.LastIndex(content, "]")

	if startIdx == -1 || endIdx == -1 || startIdx >= endIdx {
		return nil, fmt.Errorf("no valid JSON array found in response")
	}

	jsonStr := content[startIdx : endIdx+1]

	var fileNames []string
	if err := json.Unmarshal([]byte(jsonStr), &fileNames); err != nil {
		return nil, fmt.Errorf("failed to parse JSON array: %w", err)
	}

	validFileNames := make(map[string]bool)
	for _, item := range items {
		validFileNames[item.CodeFileName] = true
	}

	var result []string
	for _, fileName := range fileNames {
		if validFileNames[fileName] {
			result = append(result, fileName)
		} else {
			s.logger.Warnw("LLM returned invalid file name", "file_name", fileName)
		}
	}

	return result, nil
}
