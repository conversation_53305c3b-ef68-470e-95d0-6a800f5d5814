package service

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"

	"go.uber.org/zap"

	"web-coder-app/configs"
	"web-coder-app/internal/data/ent"
	"web-coder-app/internal/dto/request"
	"web-coder-app/internal/dto/response"
	"web-coder-app/pkg/litellm"
)

type AnalysisService struct {
	logger    *zap.SugaredLogger
	conf      *configs.Config
	llmClient *litellm.Client
}

func NewAnalysisService(opt *Options, llmClient *litellm.Client) *AnalysisService {
	return &AnalysisService{
		logger:    opt.Log,
		conf:      opt.Conf,
		llmClient: llmClient,
	}
}

// AnalyzeFiles analyzes files based on user context and returns relevant file names
func (s *AnalysisService) AnalyzeFiles(ctx context.Context, user *ent.User, req *request.AnalysisRequest) (*response.AnalysisResponse, error) {
	s.logger.Infow("Starting file analysis", "user_id", user.ID, "items_count", len(req.Items), "user_context", req.UserContext)

	prompt := s.buildAnalysisPrompt(req)
	maxTokens := s.conf.ChatModel.MaxTokens
	if maxTokens <= 0 {
		maxTokens = 2048
	}

	llmReq := &litellm.Request{
		Model: s.conf.ChatModel.Chat,
		Messages: []litellm.Message{
			{
				Role:    "system",
				Content: s.getSystemPrompt(),
			},
			{
				Role:    "user",
				Content: prompt,
			},
		},
		Stream:      false,
		Temperature: litellm.Float64Ptr(0.3), // Lower temperature for more consistent analysis
		MaxTokens:   litellm.IntPtr(maxTokens),
		Extra: map[string]interface{}{
			"response_format": map[string]string{
				"type": "json",
			},
		},
	}

	resp, err := s.llmClient.Complete(ctx, llmReq)
	if err != nil {
		s.logger.Errorw("LLM analysis failed", "error", err, "user_id", user.ID)
		return nil, fmt.Errorf("failed to analyze files: %w", err)
	}

	relevantFiles, err := s.parseAnalysisResponse(resp.Content, req.Items)
	if err != nil {
		s.logger.Errorw("Failed to parse analysis response", "error", err, "response", resp.Content)
		return nil, fmt.Errorf("failed to parse analysis response: %w", err)
	}

	s.logger.Infow("File analysis completed",
		"user_id", user.ID,
		"total_files", len(req.Items),
		"relevant_files", len(relevantFiles),
		"result", relevantFiles)

	return response.NewAnalysisResponse(relevantFiles), nil
}

// buildAnalysisPrompt builds the prompt for file analysis
func (s *AnalysisService) buildAnalysisPrompt(req *request.AnalysisRequest) string {
	var builder strings.Builder

	builder.WriteString("用户需求: ")
	builder.WriteString(req.UserContext)
	builder.WriteString("\n\n")

	builder.WriteString("可选文件列表:\n")
	for i, item := range req.Items {
		builder.WriteString(fmt.Sprintf("%d. 文件名: %s\n   描述: %s\n",
			i+1, item.CodeFileName, item.CodeDescription))
	}

	builder.WriteString("\n请分析用户需求，从上述文件列表中筛选出相关的文件名。")

	return builder.String()
}

// getSystemPrompt returns the system prompt for analysis
func (s *AnalysisService) getSystemPrompt() string {
	return `你是一个代码文件分析专家。你的任务是根据用户的需求上下文，从给定的文件列表中筛选出最相关的文件。

分析规则:
1. 仔细理解用户的需求和意图
2. 分析每个文件的描述和文件名
3. 筛选出与用户需求最相关的文件
4. 优先考虑直接相关的文件，其次考虑间接相关的文件

输出要求:
- 只返回一个JSON数组，包含相关的文件名
- 格式: ["file1.cpp", "file2.h", ...]
- 不要包含任何其他文本或解释
- 如果没有相关文件，返回空数组 []

示例输出:
["test.cpp", "main.cpp"]`
}

// parseAnalysisResponse parses the LLM response and extracts relevant file names
func (s *AnalysisService) parseAnalysisResponse(content string, items []request.AnalysisItem) ([]string, error) {
	// Clean the response content
	content = strings.TrimSpace(content)

	// Try to find JSON array in the response
	startIdx := strings.Index(content, "[")
	endIdx := strings.LastIndex(content, "]")

	if startIdx == -1 || endIdx == -1 || startIdx >= endIdx {
		return nil, fmt.Errorf("no valid JSON array found in response")
	}

	jsonStr := content[startIdx : endIdx+1]

	var fileNames []string
	if err := json.Unmarshal([]byte(jsonStr), &fileNames); err != nil {
		return nil, fmt.Errorf("failed to parse JSON array: %w", err)
	}

	// Validate that returned file names exist in the original items
	validFileNames := make(map[string]bool)
	for _, item := range items {
		validFileNames[item.CodeFileName] = true
	}

	var result []string
	for _, fileName := range fileNames {
		if validFileNames[fileName] {
			result = append(result, fileName)
		} else {
			s.logger.Warnw("LLM returned invalid file name", "file_name", fileName)
		}
	}

	return result, nil
}
