package service

import (
	"context"
	"fmt"
	"strings"
	"time"

	"web-coder-app/configs"
	"web-coder-app/internal/data/ent"
	"web-coder-app/internal/dto/request"
	"web-coder-app/internal/dto/response"
	"web-coder-app/pkg/litellm"

	"github.com/Sider-ai/go-pkg/siderllm"
	"go.uber.org/zap"
)

type SimpleChatService struct {
	logger          *zap.SugaredLogger
	conf            *configs.Config
	llmClient       *litellm.Client
	siderChatClient *siderllm.Client
}

const (
	MaxTokens       = 100000 // 最大token数（根据模型调整）
	MaxMessages     = 50     // 最大消息数量
	ReservedTokens  = 20000  // 为系统提示和响应预留的token
	AvgTokenPerChar = 0.25   // 平均每字符的token数（粗略估算）
)

// NewSimpleChatService 创建简单聊天服务实例
func NewSimpleChatService(opt *Options) *SimpleChatService {
	return &SimpleChatService{
		llmClient:       opt.LLMClient,
		siderChatClient: opt.SiderChatClient,
		logger:          opt.Log,
		conf:            opt.Conf,
	}
}

// ChatStream 处理聊天流式请求，只返回文本和推理消息
func (s *SimpleChatService) ChatStream(ctx context.Context, user *ent.User, req *request.ChatRequest) (<-chan string, error) {
	responseChan := make(chan string, 100)

	go func() {
		defer close(responseChan)
		if err := s.processSimpleChat(ctx, user, req, responseChan); err != nil {
			s.logger.Errorw("Error processing simple chat", "error", err, "user_id", user.ID)
		}
	}()

	return responseChan, nil
}

// processSimpleChat 处理简单聊天流程
func (s *SimpleChatService) processSimpleChat(ctx context.Context, user *ent.User, req *request.ChatRequest, responseChan chan<- string) error {
	messageID := response.GenerateMessageID()
	s.logger.Infow("Simple chat stream started", "user_id", user.ID, "message_id", messageID)

	llmReq, err := s.buildLiteLLMRequest(ctx, req)
	if err != nil {
		return err
	}

	fmt.Println("================ 上下文1 =============")
	for i, message := range llmReq.Messages {
		fmt.Printf("[%d]role: %s, content: %s\n\n", i, message.Role, message.Content)
	}
	fmt.Println("================ 上下文2 =============")

	stream, err := s.llmClient.Stream(ctx, llmReq)
	if err != nil {
		return fmt.Errorf("failed to start LLM stream: %w", err)
	}
	defer stream.Close()

	var sb strings.Builder

	for {
		select {
		case <-ctx.Done():
			return ctx.Err()
		default:
			chunk, err := stream.Read()
			if err != nil {
				if chunk != nil && chunk.Done {
					s.logger.Infow("Simple chat stream completed", "user_id", user.ID, "message_id", messageID)
					return nil
				}
				// 记录详细的错误信息
				s.logger.Errorw("LLM stream read error",
					"error", err,
					"user_id", user.ID,
					"message_id", messageID,
					"model", s.conf.ChatModel.Chat)
				return fmt.Errorf("LLM stream error: %w", err)
			}

			if chunk.Done {
				fmt.Println(sb.String())
				s.logger.Infow("Simple chat stream completed", "user_id", user.ID, "message_id", messageID)
				return nil
			}

			// 只处理文本和推理消息，忽略工具调用
			switch chunk.Type {
			case litellm.ChunkTypeContent:
				if chunk.Content != "" {
					sb.WriteString(chunk.Content)
					msg := response.NewTextMessage(messageID, chunk.Content)
					select {
					case responseChan <- msg.ToJSON():
					case <-ctx.Done():
						return ctx.Err()
					}
				}

			case litellm.ChunkTypeReasoning:
				if chunk.Reasoning != nil {
					var reasoningText string
					// 优先使用Content，如果没有则使用Summary
					if chunk.Reasoning.Content != "" {
						reasoningText = chunk.Reasoning.Content
					} else if chunk.Reasoning.Summary != "" {
						reasoningText = chunk.Reasoning.Summary
					}

					if reasoningText != "" {
						msg := response.NewReasoningMessage(messageID, reasoningText)
						select {
						case responseChan <- msg.ToJSON():
						case <-ctx.Done():
							return ctx.Err()
						}
					}
				}

			case litellm.ChunkTypeToolCall:
				s.logger.Debugw("Ignoring tool call in simple chat", "user_id", user.ID, "message_id", messageID)
			}
		}
	}
}

// buildLiteLLMRequest 构建LiteLLM请求
func (s *SimpleChatService) buildLiteLLMRequest(ctx context.Context, req *request.ChatRequest) (*litellm.Request, error) {
	var messages []litellm.Message
	systemPrompt, err := s.buildSysPrompt(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to build system prompt: %w", err)
	}
	messages = append(messages, litellm.Message{
		Role:    "system",
		Content: systemPrompt,
	})

	messages = append(messages, litellm.Message{
		Role:    "system",
		Content: s.buildNocacheSysPrompt(req),
	})

	// 处理对话历史消息
	optimizedMessages := s.optimizeMessageHistory(req.Messages)
	for _, msg := range optimizedMessages {
		messages = append(messages, litellm.Message{
			Role:    msg.Role,
			Content: msg.Content,
		})
	}

	maxTokens := s.conf.ChatModel.MaxTokens
	if maxTokens <= 0 {
		maxTokens = 40960
	}

	llmReq := &litellm.Request{
		Model:       s.conf.ChatModel.Chat,
		Messages:    messages,
		Stream:      true,
		Temperature: litellm.Float64Ptr(0.7),
		MaxTokens:   litellm.IntPtr(maxTokens),
	}

	return llmReq, nil
}

func (s *SimpleChatService) buildSysPrompt(ctx context.Context) (string, error) {
	sysPromptTemplates, err := s.siderChatClient.ConvertPromptTemplate(ctx, &siderllm.PromptTemplateReq{
		PromptTemplates: []siderllm.PromptTemplateParam{
			{
				Key: "agent_webpage_react",
				Attributes: map[string]string{
					"today": time.Now().Format(time.DateOnly),
				},
			},
		},
	})
	if err != nil {
		s.logger.Error("convert prompt template error", "err", err)
		return "", err
	}
	return sysPromptTemplates.SystemPrompt, nil
}

func (s *SimpleChatService) buildNocacheSysPrompt(req *request.ChatRequest) string {
	var prompt strings.Builder
	for _, item := range req.PromptTemplates {
		for key, val := range item.Attributes {
			var text string
			switch key {
			case "files":
				text = "<CurrentFiles>\n\t\t\tThe following are the currently uploaded file contents: " + val + "\n</CurrentFiles>"
			case "code":
				text = "<CurrentCode>\n\t\t\tThe following is the current code content: " + val + "\n</CurrentCode>"
			}
			prompt.WriteString(text)
		}
	}
	prompt.WriteString("<CurrentDate>\n\t\t\tCurrent time is: " + time.Now().Format(time.DateTime) + "\n</CurrentDate>")
	return prompt.String()
}

// optimizeMessageHistory 防止token超限
func (s *SimpleChatService) optimizeMessageHistory(messages []request.ChatMessage) []request.ChatMessage {
	if len(messages) == 0 {
		return messages
	}

	if len(messages) > MaxMessages {
		s.logger.Infow("Message count exceeds limit, truncating",
			"original_count", len(messages),
			"max_count", MaxMessages)
		messages = messages[len(messages)-MaxMessages:]
	}

	estimatedTokens := s.estimateTokens(messages)
	availableTokens := MaxTokens - ReservedTokens

	if estimatedTokens <= availableTokens {
		return messages
	}

	s.logger.Infow("Estimated tokens exceed limit, optimizing",
		"estimated_tokens", estimatedTokens,
		"available_tokens", availableTokens)

	return s.truncateMessages(messages, availableTokens)
}

// estimateTokens 估算消息的token数量
func (s *SimpleChatService) estimateTokens(messages []request.ChatMessage) int {
	totalChars := 0
	for _, msg := range messages {
		totalChars += len(msg.Content)
	}
	return int(float64(totalChars) * AvgTokenPerChar)
}

// intelligentTruncate 截断消息历史
func (s *SimpleChatService) truncateMessages(messages []request.ChatMessage, maxTokens int) []request.ChatMessage {
	if len(messages) == 0 {
		return messages
	}

	// 保留最后一条用户消息
	lastUserMsg := messages[len(messages)-1]
	result := []request.ChatMessage{lastUserMsg}
	currentTokens := s.estimateTokens(result)

	// 从后往前添加消息，优先保留用户消息和最近的AI回复
	for i := len(messages) - 2; i >= 0; i-- {
		msg := messages[i]
		msgTokens := int(float64(len(msg.Content)) * AvgTokenPerChar)

		if currentTokens+msgTokens > maxTokens {
			break
		}

		// 插入到开头，保持时间顺序
		result = append([]request.ChatMessage{msg}, result...)
		currentTokens += msgTokens
	}

	s.logger.Infow("Message history truncated",
		"original_count", len(messages),
		"final_count", len(result),
		"estimated_tokens", currentTokens)

	return result
}
