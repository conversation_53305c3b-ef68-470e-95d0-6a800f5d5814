package service

import (
	"context"
	"fmt"
	"strings"
	"testing"

	"go.uber.org/zap"

	"web-coder-app/configs"
	"web-coder-app/internal/dto/request"
	"web-coder-app/pkg/litellm"
)

func TestChatService_buildLiteLLMRequest(t *testing.T) {
	// Create a mock logger
	logger := zap.NewNop().Sugar()

	// Create a mock LiteLLM client
	client := litellm.New()

	// Create ChatService
	service := &ChatToolsService{
		llmClient: client,
		logger:    logger,
		conf: &configs.Config{
			ChatModel: configs.ChatModel{
				Chat: "claude-4-sonnet",
			},
		},
	}

	// Test data - simple chat request
	req := &request.ChatRequest{
		Messages: []request.ChatMessage{
			{
				Role:    "user",
				Content: "做一个博客",
			},
		},
	}

	// Test buildLiteLLMRequest
	llmReq := service.buildLiteLLMRequest(req)

	// Verify the request
	if llmReq.Model != "claude-4-sonnet" {
		t.<PERSON><PERSON><PERSON>("Expected model 'claude-4-sonnet', got '%s'", llmReq.Model)
	}

	if !llmReq.Stream {
		t.Error("Expected Stream to be true")
	}

	// Should have at least the user message
	if len(llmReq.Messages) < 1 {
		t.Errorf("Expected at least 1 message, got %d", len(llmReq.Messages))
	}

	// Find the user message
	var userMessage *litellm.Message
	for _, msg := range llmReq.Messages {
		if msg.Role == "user" {
			userMessage = &msg
			break
		}
	}

	if userMessage == nil {
		t.Error("Expected to find a user message")
	} else if userMessage.Content != "做一个博客" {
		t.Errorf("Expected content '做一个博客', got '%s'", userMessage.Content)
	}
}

func TestChatService_executeTool(t *testing.T) {
	// Create a mock logger
	logger := zap.NewNop().Sugar()

	// Create a mock LiteLLM client
	client := litellm.New()

	// Create ChatService
	service := &ChatToolsService{
		llmClient: client,
		logger:    logger,
		conf: &configs.Config{
			ChatModel: configs.ChatModel{
				Chat: "claude-4-sonnet",
			},
		},
	}

	// Test search tool
	ctx := context.Background()
	arguments := `{"query": "test search"}`

	result, err := service.executeTool(ctx, "search", arguments)
	if err != nil {
		t.Errorf("Expected no error, got %v", err)
	}

	if result == nil {
		t.Error("Expected result to not be nil")
	}

	// Test unknown tool
	_, err = service.executeTool(ctx, "unknown_tool", arguments)
	if err == nil {
		t.Error("Expected error for unknown tool")
	}
}

func TestChatService_optimizeMessageHistory(t *testing.T) {
	// Create a mock logger
	logger := zap.NewNop().Sugar()

	// Create ChatService
	service := &SimpleChatService{
		logger: logger,
		conf: &configs.Config{
			ChatModel: configs.ChatModel{
				Chat: "claude-4-sonnet",
			},
		},
	}

	// Test case 1: Normal message count
	normalMessages := []request.ChatMessage{
		{Role: "user", Content: "Hello"},
		{Role: "assistant", Content: "Hi there!"},
		{Role: "user", Content: "How are you?"},
	}

	result := service.optimizeMessageHistory(normalMessages)
	if len(result) != 3 {
		t.Errorf("Expected 3 messages, got %d", len(result))
	}

	// Test case 2: Too many messages
	manyMessages := make([]request.ChatMessage, 60) // Exceeds MaxMessages (50)
	for i := 0; i < 60; i++ {
		role := "user"
		if i%2 == 1 {
			role = "assistant"
		}
		manyMessages[i] = request.ChatMessage{
			Role:    role,
			Content: fmt.Sprintf("Message %d", i),
		}
	}

	result = service.optimizeMessageHistory(manyMessages)
	if len(result) > MaxMessages {
		t.Errorf("Expected at most %d messages, got %d", MaxMessages, len(result))
	}

	// Should keep the latest messages
	if result[len(result)-1].Content != "Message 59" {
		t.Errorf("Expected last message to be 'Message 59', got '%s'", result[len(result)-1].Content)
	}

	// Test case 3: Very long messages (token limit)
	longMessages := []request.ChatMessage{
		{Role: "user", Content: strings.Repeat("This is a very long message. ", 10000)}, // ~300k chars
		{Role: "assistant", Content: "Short reply"},
		{Role: "user", Content: "Final question"},
	}

	result = service.optimizeMessageHistory(longMessages)
	// Should keep the final user message and possibly truncate earlier ones
	if len(result) == 0 {
		t.Error("Expected at least one message to remain")
	}

	// The last message should always be preserved
	if result[len(result)-1].Content != "Final question" {
		t.Errorf("Expected last message to be 'Final question', got '%s'", result[len(result)-1].Content)
	}

	// Should have fewer than 3 messages due to token limit
	if len(result) == 3 {
		t.Log("All messages kept - this is fine if total tokens are within limit")
	}
}
