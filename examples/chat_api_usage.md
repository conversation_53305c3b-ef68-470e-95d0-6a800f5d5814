# Chat API 使用示例

## 新的多轮对话支持

现在 Chat API 支持多轮对话，前端可以发送完整的对话历史。

### API 端点
```
POST /v1/chat/stream
```

### 请求格式

#### 单轮对话
```json
{
  "messages": [
    {
      "role": "user",
      "content": "帮我做一个博客网站"
    }
  ]
}
```

#### 多轮对话
```json
{
  "messages": [
    {
      "role": "user", 
      "content": "帮我做一个博客网站"
    },
    {
      "role": "assistant",
      "content": "好的，我来帮你创建一个博客网站。我会使用现代的技术栈来构建..."
    },
    {
      "role": "user",
      "content": "请把主题颜色改成蓝色"
    }
  ]
}
```

#### 带工具调用的对话
```json
{
  "messages": [
    {
      "role": "user",
      "content": "帮我做一个React博客，需要安装必要的依赖"
    }
  ],
  "toolsParam": {
    "auto": ["web-coder-npmInstall", "search"]
  }
}
```

### 消息角色说明

- `user`: 用户消息
- `assistant`: AI助手回复
- `system`: 系统提示（通常由后端自动添加）

### 前端实现建议

```javascript
class ChatManager {
  constructor() {
    this.messages = [];
  }

  async sendMessage(userMessage) {
    // 添加用户消息到历史
    this.messages.push({
      role: "user",
      content: userMessage
    });

    // 发送请求
    const response = await fetch('/v1/chat/stream', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ' + token
      },
      body: JSON.stringify({
        messages: this.messages
      })
    });

    // 处理流式响应
    const reader = response.body.getReader();
    let assistantMessage = "";
    
    while (true) {
      const { done, value } = await reader.read();
      if (done) break;
      
      const chunk = new TextDecoder().decode(value);
      // 处理 SSE 数据
      assistantMessage += chunk;
    }

    // 添加助手回复到历史
    this.messages.push({
      role: "assistant", 
      content: assistantMessage
    });
  }
}
```

### 优势

1. **完整上下文**: AI 可以理解整个对话历史
2. **连续对话**: 支持"修改颜色"、"添加功能"等后续请求
3. **前端控制**: 前端完全控制对话历史和上下文
4. **简洁设计**: 后端无需存储会话状态
5. **灵活性**: 前端可以选择性发送历史消息

## 历史消息自动优化

后端已实现智能的历史消息管理，无需前端担心 token 超限问题：

### 自动优化策略

1. **数量限制**: 最多保留 50 条消息
2. **Token 估算**: 自动估算消息的 token 使用量
3. **智能截断**: 当超过限制时，优先保留：
   - 最新的用户消息（最重要）
   - 最近的对话历史
   - 保持对话的连贯性

### 配置参数

```go
const (
    MaxTokens       = 100000 // 最大token数
    MaxMessages     = 50     // 最大消息数量
    ReservedTokens  = 20000  // 为系统提示预留
    AvgTokenPerChar = 0.25   // 每字符平均token数
)
```

### 优化日志

当消息被优化时，后端会记录日志：

```
Message count exceeds limit, truncating original_count=60 max_count=50
Estimated tokens exceed limit, optimizing estimated_tokens=120000 available_tokens=80000
Message history truncated original_count=50 final_count=35 estimated_tokens=75000
```

### 前端最佳实践

```javascript
class ChatManager {
  constructor() {
    this.messages = [];
    this.maxLocalMessages = 100; // 前端也可以做预处理
  }

  async sendMessage(userMessage) {
    // 前端预处理：保留最近的消息
    if (this.messages.length > this.maxLocalMessages) {
      this.messages = this.messages.slice(-this.maxLocalMessages);
    }

    this.messages.push({
      role: "user",
      content: userMessage
    });

    // 后端会自动优化，无需担心
    const response = await this.callAPI();

    // 处理响应...
  }

  // 可选：前端也可以实现简单的优化
  optimizeMessages() {
    // 移除过老的消息，但保留重要的上下文
    if (this.messages.length > 50) {
      // 保留最新的40条消息
      this.messages = this.messages.slice(-40);
    }
  }
}
```

### 注意事项

1. **自动处理**: 后端会自动处理 token 超限，前端无需特殊处理
2. **性能优化**: 建议前端也做适当的消息数量控制
3. **重要消息**: 最新的用户消息总是会被保留
4. **日志监控**: 可以通过日志监控优化情况
5. **成本控制**: 自动优化有助于控制 API 调用成本
