#!/bin/bash

# Analysis API 测试脚本

# 服务器地址
BASE_URL="http://localhost:8082"

# 测试用的 Token (需要替换为实际的 token)
TOKEN="your-jwt-token-here"

echo "=== Analysis API 测试 ==="
echo

# 测试数据
TEST_DATA='{
    "items": [
        {
            "codeDescription": "测试用例文件",
            "codeFileName": "test.cpp"
        },
        {
            "codeDescription": "主函数",
            "codeFileName": "main.cpp"
        },
        {
            "codeDescription": "工具类库",
            "codeFileName": "utils.h"
        },
        {
            "codeDescription": "配置文件",
            "codeFileName": "config.json"
        }
    ],
    "userContext": "我需要修改测试用例"
}'

echo "发送请求到: $BASE_URL/v1/analysis"
echo "请求数据:"
echo "$TEST_DATA" | jq .
echo

# 发送请求
response=$(curl -s -X POST \
  "$BASE_URL/v1/analysis" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d "$TEST_DATA")

echo "响应结果:"
echo "$response" | jq .
echo

# 检查响应状态
if echo "$response" | jq -e '.code == 0' > /dev/null; then
    echo "✅ 测试成功！"
    echo "筛选出的文件:"
    echo "$response" | jq -r '.data.items[]'
else
    echo "❌ 测试失败！"
    echo "错误信息: $(echo "$response" | jq -r '.message')"
fi
