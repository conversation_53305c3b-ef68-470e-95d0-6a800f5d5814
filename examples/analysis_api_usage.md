# Analysis API 使用示例

## 文件分析接口

通过大模型分析用户需求，从给定的文件列表中筛选出相关的文件。

### API 端点
```
POST /v1/analysis
```

### 请求头
```
Authorization: Bearer <your-token>
Content-Type: application/json
```

### 请求格式

```json
{
    "items": [
        {
            "codeDescription": "测试用例文件",
            "codeFileName": "test.cpp"
        },
        {
            "codeDescription": "主函数",
            "codeFileName": "main.cpp"
        },
        {
            "codeDescription": "工具类库",
            "codeFileName": "utils.h"
        },
        {
            "codeDescription": "配置文件",
            "codeFileName": "config.json"
        }
    ],
    "userContext": "我需要修改测试用例"
}
```

### 响应格式

#### 成功响应
```json
{
    "code": 0,
    "message": "success",
    "data": {
        "items": [
            "test.cpp"
        ]
    }
}
```

#### 错误响应
```json
{
    "code": 1000,
    "message": "Invalid request format: items cannot be empty"
}
```

### 请求参数说明

| 字段 | 类型 | 必填 | 说明 |
|------|------|------|------|
| items | Array | 是 | 文件列表，至少包含一个文件 |
| items[].codeDescription | String | 是 | 文件描述，说明文件的功能和用途 |
| items[].codeFileName | String | 是 | 文件名 |
| userContext | String | 是 | 用户需求上下文，描述要做什么 |

### 响应参数说明

| 字段 | 类型 | 说明 |
|------|------|------|
| code | Integer | 状态码，0表示成功 |
| message | String | 响应消息 |
| data.items | Array | 筛选出的相关文件名列表 |

### 使用场景示例

#### 1. 修改测试用例
```json
{
    "items": [
        {"codeDescription": "单元测试", "codeFileName": "unit_test.cpp"},
        {"codeDescription": "集成测试", "codeFileName": "integration_test.cpp"},
        {"codeDescription": "主程序", "codeFileName": "main.cpp"},
        {"codeDescription": "配置文件", "codeFileName": "config.yaml"}
    ],
    "userContext": "我需要修改单元测试中的登录测试用例"
}
```

#### 2. 添加新功能
```json
{
    "items": [
        {"codeDescription": "用户管理模块", "codeFileName": "user_manager.py"},
        {"codeDescription": "数据库操作", "codeFileName": "database.py"},
        {"codeDescription": "API路由", "codeFileName": "routes.py"},
        {"codeDescription": "前端页面", "codeFileName": "index.html"}
    ],
    "userContext": "我要添加用户注册功能"
}
```

#### 3. 修复Bug
```json
{
    "items": [
        {"codeDescription": "支付模块", "codeFileName": "payment.js"},
        {"codeDescription": "订单处理", "codeFileName": "order.js"},
        {"codeDescription": "用户界面", "codeFileName": "checkout.vue"},
        {"codeDescription": "日志记录", "codeFileName": "logger.js"}
    ],
    "userContext": "修复支付失败的问题"
}
```

### 错误码说明

| 错误码 | 说明 |
|--------|------|
| 0 | 成功 |
| 1000 | 请求参数错误 |
| 1001 | Token无效 |
| 500 | 服务器内部错误 |

### 技术特性

1. **动态系统提示词**: 从服务中获取 `agent_web_coder_analysis` 提示词模板
2. **结构化输出**: 使用 GLM-4.5 的 JSON 模式确保返回格式的一致性
3. **智能分析**: 通过大模型理解用户需求和文件描述的语义关联
4. **结果验证**: 自动验证返回的文件名是否在原始列表中
5. **错误恢复**: 具备 JSON 解析失败的容错机制

### 注意事项

1. **认证要求**: 需要有效的JWT Token
2. **文件数量**: 建议单次请求的文件数量不超过50个
3. **描述质量**: 文件描述越详细，分析结果越准确
4. **用户上下文**: 需求描述要清晰具体，避免模糊表达
5. **响应时间**: 通常在2-5秒内返回结果，取决于文件数量和复杂度
6. **模型要求**: 当前使用 GLM-4.5 模型，支持原生 JSON 输出格式
