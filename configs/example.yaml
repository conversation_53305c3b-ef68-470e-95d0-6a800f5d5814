App:
  Addr: ":8082"
  Mode: release
  GeoLite2DB: "resources/bin/GeoLite2-Country.mmdb"

DB:
  Dialect: mysql
  DSN: user:passwd@tcp(127.0.0.1:3306)/database?charset=utf8mb4&parseTime=True&loc=Local
  MaxIdle: 100
  MaxActive: 100
  MaxLifetime: 300
  AutoMigrate: false

Redis:
  Addr: 127.0.0.1:6379
  DB: 0
  Password: ""

Log:
  FileDir: log
  MaxSize: 100
  MaxBackups: 5
  MaxAge: 60
  Compress: false
  LocalTime: false
  AddSource: true


Jwt:
  Secret: "Sider_imagezero_adsiapoiepwoq&*(^&^&*6_test"
  Issuer: "dev.imagezero.ai"

Payment:
  ApplePrivateKey: "xxxxxx"

ImageService:
  ResponseStoragePath: "./tmp/imagezero/responses"

Claude:
  APIKey: "your-claude-api-key"
  BaseURL: "https://api.anthropic.com"

ChatModel:
  Chat: "glm-4.5"
  MaxTokens: 4096