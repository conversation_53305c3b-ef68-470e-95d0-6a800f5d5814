package configs

import (
	"fmt"
	"os"
	"strings"

	"github.com/Sider-ai/go-pkg/siderllm"

	"github.com/Sider-ai/go-pkg/xoss"
	"github.com/spf13/viper"

	"web-coder-app/pkg/hashidv2"
	"web-coder-app/pkg/logger"
)

type Config struct {
	Jwt        Jwt           `yaml:"Jwt"`
	App        App           `yaml:"App"`
	DB         DB            `yaml:"DB"`
	Redis      Redis         `yaml:"Redis"`
	CacheRedis Redis         `yaml:"CacheRedis"`
	HashID     HashID        `yaml:"HashID"`
	Log        logger.Config `yaml:"Log"`
	// FileCloudFront CloudFront    `yaml:"FileCloudFront"`
	OSS       xoss.Config     `yaml:"OSS"`
	Claude    Claude          `yaml:"Claude"`
	ChatModel ChatModel       `yaml:"ChatModel"`
	SiderLLM  siderllm.Config `yaml:"SiderLLM"`
}

type ChatModel struct {
	Chat      string `yaml:"Chat"`
	MaxTokens int    `yaml:"MaxTokens"`
}

type Jwt struct {
	Secret string `yaml:"Secret"`
	Issuer string `yaml:"Issuer"`
}

// type CloudFront struct {
//	PublicKeyID  string `yaml:"PublicKeyID"`
//	PrivKeyPath  string `yaml:"PrivKeyPath"`
//	Domain       string `yaml:"Domain"`
//	CustomDomain string `yaml:CustomDomain"`
// }

type App struct {
	Addr       string `yaml:"Addr"`
	Mode       string `yaml:"Mode"`
	GeoLite2DB string `yaml:"GeoLite2DB"`
}

type DB struct {
	Dialect     string `yaml:"Dialect"`
	DSN         string `yaml:"DSN"`
	MaxIdle     int    `yaml:"MaxIdle"`
	MaxActive   int    `yaml:"MaxActive"`
	MaxLifetime int    `yaml:"MaxLifetime"`
	AutoMigrate bool   `yaml:"AutoMigrate"`
}

type Redis struct {
	Addr     string `yaml:"Addr"`
	DB       int    `yaml:"DB"`
	Password string `yaml:"Password"`
}

type HashID struct {
	User                hashidv2.Config `yaml:"User"`
	Share               hashidv2.Config `yaml:"Share"`
	Conversation        hashidv2.Config `yaml:"Conversation"`
	Embedding           hashidv2.Config `yaml:"Embedding"`
	ConversationMessage hashidv2.Config `yaml:"ConversationMessage"`
	UserImage           hashidv2.Config `yaml:"UserImage"`
	ChatShare           hashidv2.Config `yaml:"ChatShare"`
	UserMirror          hashidv2.Config `yaml:"UserMirror"`
	Ask                 hashidv2.Config `yaml:"Ask"`
	AskAnswer           hashidv2.Config `yaml:"AskAnswer"`
	DictionaryEntry     hashidv2.Config `yaml:"DictionaryEntry"`
	// OCR                 hashidv2.Config `yaml:"OCR"`
	// AD                  hashidv2.Config `yaml:"AD"`
}

type Claude struct {
	APIKey  string `yaml:"APIKey"`
	BaseURL string `yaml:"BaseURL"`
}

func (c *Config) IsDebugMode() bool {
	return c.App.Mode == "debug" || c.App.Mode == "local"
}

func (c *Config) IsReleaseMode() bool {
	return c.App.Mode == "release" || c.App.Mode == "prod"
}

func InitConfig() (*Config, error) {
	var cfg Config
	mode := os.Getenv("APP_MODE")
	if mode == "" {
		mode = "prod"
	}
	configPath := fmt.Sprintf("configs/%s.yaml", mode)
	file, err := os.Open(configPath)
	if err != nil {
		return nil, err
	}
	defer file.Close()

	viper.SetConfigType("yaml")
	viper.SetEnvKeyReplacer(strings.NewReplacer(".", "_"))
	viper.AutomaticEnv()
	if err := viper.ReadConfig(file); err != nil {
		return nil, err
	}
	if err := viper.Unmarshal(&cfg); err != nil {
		return nil, err
	}
	return &cfg, err
}
