-- PostgreSQL CREATE TABLE statements generated from schema files
-- 生成时间: $(date)

-- 1. app_configs 表
CREATE TABLE IF NOT EXISTS app_configs (
    id BIGSERIAL PRIMARY KEY,
    key VARCHAR(255) NOT NULL,
    value TEXT NOT NULL,
    value_type VA<PERSON>HAR(10) NOT NULL DEFAULT 'string',
    type VARCHAR(10) NOT NULL DEFAULT 'client',
    app_name VARCHAR(255) NOT NULL DEFAULT '',
    app_version_gte VARCHAR(255) NOT NULL DEFAULT '',
    app_version_lte VARCHAR(255) NOT NULL DEFAULT '',
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    delete_time TIMESTAMP NULL,
    
    -- 创建唯一索引
    CONSTRAINT uk_app_configs_key_app_name UNIQUE (key, app_name)
);

-- 插入初始数据
INSERT INTO app_configs ("id", "create_time", "update_time", "delete_time", "key", "value", "value_type", "type", "app_name", "app_version_gte", "app_version_lte") 
VALUES (1, '2025-08-03 17:52:39+08', '2025-08-03 17:52:45+08', NULL, 'device_secret', '"b7a70a1e474f41b33eff90956032bdaf"', 'string', 'server', '', '', '');

-- 2. users 表
CREATE TABLE IF NOT EXISTS users (
    id BIGSERIAL PRIMARY KEY,
    nickname VARCHAR(255) NOT NULL DEFAULT '',
    register_type VARCHAR(20) NOT NULL DEFAULT 'device',
    device_id VARCHAR(255) NOT NULL,
    register_ip VARCHAR(255) NOT NULL,
    register_region VARCHAR(255) NOT NULL DEFAULT '',
    email VARCHAR(255) NOT NULL DEFAULT '',
    phone VARCHAR(255) NOT NULL DEFAULT '',
    email_verified BOOLEAN NOT NULL DEFAULT FALSE,
    password VARCHAR(255) NOT NULL DEFAULT '',
    register_app_name VARCHAR(50) NOT NULL,
    register_app_version VARCHAR(255) NOT NULL DEFAULT '',
    avatar VARCHAR(255) NOT NULL DEFAULT '',
    profile VARCHAR(255) NOT NULL DEFAULT '',
    register_from VARCHAR(255) NOT NULL DEFAULT '',
    bind_at TIMESTAMP NULL,
    transfer_user_id INTEGER NOT NULL DEFAULT 0,
    blocked BOOLEAN NOT NULL DEFAULT FALSE,
    extra JSONB NULL,
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    delete_time TIMESTAMP NULL
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_users_device_id ON users(device_id);
CREATE INDEX IF NOT EXISTS idx_users_register_type ON users(register_type);
CREATE INDEX IF NOT EXISTS idx_users_register_ip ON users(register_ip);
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_phone ON users(phone);
CREATE INDEX IF NOT EXISTS idx_users_register_app_name ON users(register_app_name);

-- 3. app_logs 表
CREATE TABLE IF NOT EXISTS app_logs (
    id BIGSERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL DEFAULT 0,
    trace_id VARCHAR(255) NOT NULL DEFAULT '',
    ip VARCHAR(255) NOT NULL DEFAULT '',
    method VARCHAR(255) NOT NULL DEFAULT '',
    path VARCHAR(255) NOT NULL DEFAULT '',
    query TEXT NOT NULL DEFAULT '',
    body TEXT NOT NULL DEFAULT '',
    level VARCHAR(10) NOT NULL DEFAULT 'ERROR',
    from_source VARCHAR(10) NOT NULL DEFAULT 'api',
    err_msg TEXT NOT NULL,
    resp_err_msg TEXT NOT NULL DEFAULT '',
    code INTEGER NULL DEFAULT 0,
    service_name VARCHAR(255) NOT NULL DEFAULT '',
    extra JSONB NOT NULL,
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_app_logs_user_id ON app_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_app_logs_level ON app_logs(level);
CREATE INDEX IF NOT EXISTS idx_app_logs_path ON app_logs(path);
CREATE INDEX IF NOT EXISTS idx_app_logs_code ON app_logs(code);

-- 4. user_login_logs 表
CREATE TABLE IF NOT EXISTS user_login_logs (
    id BIGSERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL,
    token_id VARCHAR(255) NOT NULL,
    app_name VARCHAR(255) NOT NULL,
    app_version VARCHAR(255) NULL DEFAULT '',
    ip VARCHAR(255) NOT NULL,
    region VARCHAR(255) NOT NULL DEFAULT '',
    login_type VARCHAR(255) NOT NULL,
    from_source VARCHAR(255) NULL DEFAULT '',
    expire_time TIMESTAMP NULL,
    status VARCHAR(32) NOT NULL,
    extra JSONB NULL,
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    delete_time TIMESTAMP NULL,
    
    -- 创建唯一约束
    CONSTRAINT uk_user_login_logs_token_id UNIQUE (token_id)
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_user_login_logs_user_id ON user_login_logs(user_id);

-- 5. user_requests 表
CREATE TABLE IF NOT EXISTS user_requests (
    id BIGSERIAL PRIMARY KEY,
    trace_id VARCHAR(255) NOT NULL, -- Unique trace ID for the request
    user_id INTEGER NOT NULL,
    method VARCHAR(255) NOT NULL, -- HTTP method of the request
    path VARCHAR(255) NOT NULL, -- Request path
    status VARCHAR(128) NOT NULL DEFAULT 'pending', -- Current status of the request
    status_code INTEGER NULL, -- HTTP status code of the response
    req_data TEXT NULL, -- Request data including headers
    resp_data TEXT NULL, -- Response data
    error_message VARCHAR(255) NULL, -- Error message if the request failed
    received_at TIMESTAMP(3) WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP, -- Time when the request was received
    responded_at TIMESTAMP(3) WITH TIME ZONE NULL, -- Time when the response was sent
    processing_time BIGINT NULL, -- Time taken to process the request in nanoseconds
    app_name VARCHAR(128) NOT NULL DEFAULT '',
    app_version VARCHAR(128) NOT NULL DEFAULT '',
    user_ip VARCHAR(128) NOT NULL DEFAULT '',
    user_region VARCHAR(32) NOT NULL DEFAULT '',
    service_name VARCHAR(128) NOT NULL DEFAULT '',
    service_function VARCHAR(128) NOT NULL DEFAULT '',
    
    -- 创建唯一约束
    CONSTRAINT uk_user_requests_trace_id UNIQUE (trace_id)
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_user_requests_user_id ON user_requests(user_id);
CREATE INDEX IF NOT EXISTS idx_user_requests_received_at ON user_requests(received_at);
