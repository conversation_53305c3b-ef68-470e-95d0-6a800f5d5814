### Go template
# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool, specifically when used with LiteIDE
*.out

# Dependency directories (remove the comment below to include it)
# vendor/
server
app
docs
examples
.vscode
log
.idea
output/
configs/prod.yaml
configs/local.yaml
configs/test.cloudfront_private_key.pem
**/data/ent
cmd/app/wire_gen.go
resources/bin/*

!resources/bin/.gitkeep
